import { IPackageEquipment } from '@/types/package_';
import { IApiResponse } from '@/types/response';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

export function useUpdatePackageEquipment(id: string) {
  const queryClient = useQueryClient();
  return useMutation<IApiResponse<IPackageEquipment>, Error, IPackageEquipment>(
    {
      mutationFn: (data: IPackageEquipment) =>
        fetch(`/api/package-equipment/${data.id}`, {
          method: 'PATCH',
          mode: 'cors',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(data),
        }).then((res) => {
          if (!res.ok) throw new Error(res.statusText);
          return res.json();
        }),
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['packages-equipment'] });
        queryClient.invalidateQueries({ queryKey: ['packages-equipment', id] });
        toast.success('Package equipment updated successfully ');
      },
      onError: () => {
        toast.error('Error updating package equipment');
      },
    }
  );
}
