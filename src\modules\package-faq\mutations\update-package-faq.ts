import { IPackageFaq } from '@/types/package_';
import { IApiResponse } from '@/types/response';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

export function useUpdatePackageFaq(id: string) {
  const queryClient = useQueryClient();
  return useMutation<IApiResponse<IPackageFaq>, Error, IPackageFaq>({
    mutationFn: (data: IPackageFaq) =>
      fetch(`/api/package-faq/${data.id}`, {
        method: 'PATCH',
        mode: 'cors',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      }).then((res) => {
        if (!res.ok) throw new Error(res.statusText);
        return res.json();
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['packages-faq'] });
      queryClient.invalidateQueries({ queryKey: ['packages-faq', id] });
      toast.success('Package updated successfully');
    },
    onError: () => {
      toast.error('Error updating package');
    },
  });
}
