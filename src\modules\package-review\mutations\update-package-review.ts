import { IPackageReview } from '@/types/package_';
import { IApiResponse } from '@/types/response';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

export function useUpdatePackageReview(id: string) {
  const queryClient = useQueryClient();
  return useMutation<IApiResponse<IPackageReview>, Error, IPackageReview>({
    mutationFn: (data: IPackageReview) =>
      fetch(`/api/package-review/${data.id}`, {
        method: 'PATCH',
        mode: 'cors',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      }).then((res) => {
        if (!res.ok) throw new Error(res.statusText);
        return res.json();
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['packages-review'] });
      queryClient.invalidateQueries({ queryKey: ['packages-review', id] });
      toast.success('Package updated successfully');
    },
    onError: () => {
      toast.error('Error updating package');
    },
  });
}
