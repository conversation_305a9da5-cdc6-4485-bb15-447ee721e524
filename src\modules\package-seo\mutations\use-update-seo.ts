import { IPackageSEO } from '@/types/package_';
import { IApiResponse } from '@/types/response';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

export function useUpdatePackageSeo(id: string) {
  const queryClient = useQueryClient();
  return useMutation<IApiResponse<IPackageSEO>, Error, IPackageSEO>({
    mutationFn: (data: IPackageSEO) =>
      fetch(`/api/package/seo/${data.id}`, {
        method: 'PATCH',
        mode: 'cors',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      }).then((res) => {
        if (!res.ok) throw new Error(res.statusText);
        return res.json();
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['packages'] });
      queryClient.invalidateQueries({ queryKey: ['package', id] });
      toast.success('Package SEO updated successfully');
    },
    onError: () => {
      toast.error('Error updating package SEO');
    },
  });
}
