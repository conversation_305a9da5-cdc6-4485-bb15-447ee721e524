'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'

interface EditTabsProps {
    packageId: string | number
}

export function EditTabs({ packageId }: EditTabsProps) {
    const pathname = usePathname()
    const basePath = `/package/edit/${packageId}`

    const tabs = [
        { key: 'details', label: 'Details', href: `${basePath}` },
        { key: 'itinerary', label: 'Itinerary', href: `${basePath}/itinerary` },
        { key: 'equipment', label: 'Equipment', href: `${basePath}/equipment` },
        { key: 'cost', label: 'Cost and Date', href: `${basePath}/costanddate` },
        { key: 'discount', label: 'Group Discount', href: `${basePath}/packagegdp` },
        { key: 'faq', label: 'FAQ', href: `${basePath}/packagefaq` },
        { key: 'review', label: 'Review', href: `${basePath}/packagereview` },
        { key: 'seo', label: 'SEO', href: `${basePath}/seo` },
    ]

    return (
        <nav className="flex flex-col mb-4">
            <div>
                <div>
                    <h1 className="px-4 text-3xl font-bold mb-4">Edit Package</h1>
                </div>
                <div className="flex flex-wrap bg-gray-200 space-x-3 rounded-lg">
                    {tabs.map(tab => {
                        const isActive = pathname === tab.href
                        return (
                            <Link key={tab.key} href={tab.href}>
                                <p className={`px-4 py-2 ${isActive ? 'border-b-2 bg-white border-brand font-semibold text-brand rounded-lg' : 'text-gray-600'}`}>
                                    {tab.label}
                                </p>
                            </Link>
                        )
                    })}
                </div>
            </div>
        </nav>
    )
}
