export interface IHome {
  id: string;
  createdAt: string;
  updatedAt: string;
}

export interface IHero {
  id: string;
  homeId?: string;
  videoUrl: string;
  titles: string[];
  subtitles: string[];
  images: string[];
  createdAt?: string;
  updatedAt?: string;
}

export interface IAdventure {
  id: string;
  title: string;
  images: string[];
  points: string[];
  linkUrl: string;
  linkLabel: string;
}

export interface IExperienceFeature {
  id: string;
  title: string;
  subtitle: string;
}

export interface IExperience {
  id: string;
  heading: string;
  subHeading: string;
  homeId: string;
  features: IExperienceFeature[];
}

export interface IHikingAreaItem {
  id: string;
  homeHikingId: string;
  image: string;
  title: string;
  subtitle: string;
  linkUrl: string;
  createdAt: string;
  updatedAt: string;
}

export interface IHikingArea {
  id: string;
  heading: string;
  subHeading: string;
  homeId: string;
  createdAt: string;
  updatedAt: string;
  areas: IHikingAreaItem[];
}

export interface ITailoredAdventure {
  id: string;
  title: string;
  subtitle: string;
  homeId: string;
  createdAt: string;
  updatedAt: string;
  features: ITailoredAdventureFeature[];
}

export interface ITailoredAdventureFeature {
  id: string;
  homeTailoredAdventureId: string;
  title: string;
  subtitle: string;
  linkLabel: string;
  linkUrl: string;
  createdAt: string;
  updatedAt: string;
}

export interface IReviewItem {
  id: string;
  homeReviewId: string;
  quote: string;
  image: string;
  name: string;
  designation: string;
  date: string;
  createdAt: string;
  updatedAt: string;
}

export interface IReview {
  id: string;
  homeId: string;
  title: string;
  subtitle: string;
  createdAt: string;
  updatedAt: string;
  reviews: IReviewItem[];
}

export interface ITestimonial {
  id: string;
  title: string;
  subtitle: string;
  homeId: string;
  createdAt: string;
  updatedAt: string;
  testimonials: ITestimonialItem[];
}

export interface ITestimonialItem {
  id: string;
  homeVideoTestimonialId: string;
  youtubeUrl: string;
  youtubeThumbnail: string;
  title: string;
  destination: string;
  date: string;
  createdAt: string;
}

export interface IHomeSEO {
  id: string;
  homeId?: string;
  metaTitle?: string;
  metaDescription?: string;
  metaKeywords?: string[];
  canonicalUrl?: string;
  ogTitle?: string;
  ogDescription?: string;
  ogImage?: string;
  ogType?: string;
  ogUrl?: string;
  ogSiteName?: string;
  twitterTitle?: string;
  twitterDescription?: string;
  twitterImage?: string;
  twitterCard?: string;
  robots?: string;
  language?: string;
  structuredData?: string;
  sitemapPriority?: string;
  changefreq?: string;
  customMetaTags?: string;
  createdAt?: string;
  updatedAt?: string;
}
